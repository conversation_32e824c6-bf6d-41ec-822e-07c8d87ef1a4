'use strict';

/**
 * User controller for additional user operations
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::user.user', ({ strapi }) => ({
  /**
   * Get deletion impact summary for the authenticated user
   */
  async getDeletionImpact(ctx) {
    try {
      const userId = ctx.state.user.id;

      if (!userId) {
        return ctx.unauthorized('You must be logged in');
      }

      const impact = await strapi.service('api::user.user').getUserDeletionImpact(userId);

      if (!impact) {
        return ctx.internalServerError('Unable to calculate deletion impact');
      }

      ctx.send({
        message: 'Account deletion impact calculated',
        impact,
        warning: 'Deleting your account will permanently remove all this data and cannot be undone.',
      });

    } catch (error) {
      console.error('Error getting deletion impact:', error);
      ctx.throw(500, 'An error occurred while calculating deletion impact');
    }
  },
}));
