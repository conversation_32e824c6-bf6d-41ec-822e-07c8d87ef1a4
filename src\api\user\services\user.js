'use strict';

/**
 * User service for handling user-related operations
 */

module.exports = {
  /**
   * Clean up all user-related data before account deletion
   * This ensures referential integrity and removes orphaned data
   */
  async cleanupUserData(userId) {
    try {
      console.log(`🧹 Starting cleanup for user ID: ${userId}`);

      // 1. Delete user's cart
      try {
        const userCart = await strapi.entityService.findMany('api::cart.cart', {
          filters: { users_permissions_user: userId },
        });

        if (userCart && userCart.length > 0) {
          for (const cart of userCart) {
            await strapi.entityService.delete('api::cart.cart', cart.id);
            console.log(`   ✅ Deleted cart: ${cart.id}`);
          }
        }
      } catch (error) {
        console.error('   ❌ Error deleting user cart:', error.message);
      }

      // 2. Delete user's wishlist
      try {
        const userWishlist = await strapi.entityService.findMany('api::wishlist.wishlist', {
          filters: { users_permissions_user: userId },
        });

        if (userWishlist && userWishlist.length > 0) {
          for (const wishlist of userWishlist) {
            await strapi.entityService.delete('api::wishlist.wishlist', wishlist.id);
            console.log(`   ✅ Deleted wishlist: ${wishlist.id}`);
          }
        }
      } catch (error) {
        console.error('   ❌ Error deleting user wishlist:', error.message);
      }

      // 3. Delete user's orders (or mark as deleted user)
      try {
        const userOrders = await strapi.entityService.findMany('api::order.order', {
          filters: { users_permissions_user: userId },
        });

        if (userOrders && userOrders.length > 0) {
          for (const order of userOrders) {
            // Option 1: Delete orders completely
            // await strapi.entityService.delete('api::order.order', order.id);
            
            // Option 2: Keep orders for business records but anonymize
            await strapi.entityService.update('api::order.order', order.id, {
              data: {
                users_permissions_user: null,
                customerEmail: '[DELETED USER]',
                customerName: '[DELETED USER]',
                notes: `${order.notes || ''} [User account deleted on ${new Date().toISOString()}]`.trim(),
              },
            });
            console.log(`   ✅ Anonymized order: ${order.id}`);
          }
        }
      } catch (error) {
        console.error('   ❌ Error handling user orders:', error.message);
      }

      // 4. Delete user's payments
      try {
        const userPayments = await strapi.entityService.findMany('api::payment.payment', {
          filters: { users_permissions_user: userId },
        });

        if (userPayments && userPayments.length > 0) {
          for (const payment of userPayments) {
            // Keep payment records for financial compliance but anonymize
            await strapi.entityService.update('api::payment.payment', payment.id, {
              data: {
                users_permissions_user: null,
                notes: `${payment.notes || ''} [User account deleted on ${new Date().toISOString()}]`.trim(),
              },
            });
            console.log(`   ✅ Anonymized payment: ${payment.id}`);
          }
        }
      } catch (error) {
        console.error('   ❌ Error handling user payments:', error.message);
      }

      // 5. Delete user's notifications
      try {
        const userNotifications = await strapi.entityService.findMany('api::notification.notification', {
          filters: { users_permissions_user: userId },
        });

        if (userNotifications && userNotifications.length > 0) {
          for (const notification of userNotifications) {
            await strapi.entityService.delete('api::notification.notification', notification.id);
            console.log(`   ✅ Deleted notification: ${notification.id}`);
          }
        }
      } catch (error) {
        console.error('   ❌ Error deleting user notifications:', error.message);
      }

      // 6. Handle user's reviews/ratings (if you have them)
      try {
        // Example: Delete or anonymize product reviews
        // const userReviews = await strapi.entityService.findMany('api::review.review', {
        //   filters: { users_permissions_user: userId },
        // });
        // ... handle reviews
      } catch (error) {
        console.error('   ❌ Error handling user reviews:', error.message);
      }

      console.log(`✅ User data cleanup completed for user ID: ${userId}`);

    } catch (error) {
      console.error(`❌ Error during user data cleanup for user ID ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Get user deletion impact summary (optional - for showing user what will be deleted)
   */
  async getUserDeletionImpact(userId) {
    try {
      const impact = {
        cart: 0,
        wishlist: 0,
        orders: 0,
        payments: 0,
        notifications: 0,
      };

      // Count cart items
      const userCart = await strapi.entityService.findMany('api::cart.cart', {
        filters: { users_permissions_user: userId },
      });
      impact.cart = userCart ? userCart.length : 0;

      // Count wishlist items
      const userWishlist = await strapi.entityService.findMany('api::wishlist.wishlist', {
        filters: { users_permissions_user: userId },
      });
      impact.wishlist = userWishlist ? userWishlist.length : 0;

      // Count orders
      const userOrders = await strapi.entityService.findMany('api::order.order', {
        filters: { users_permissions_user: userId },
      });
      impact.orders = userOrders ? userOrders.length : 0;

      // Count payments
      const userPayments = await strapi.entityService.findMany('api::payment.payment', {
        filters: { users_permissions_user: userId },
      });
      impact.payments = userPayments ? userPayments.length : 0;

      // Count notifications
      const userNotifications = await strapi.entityService.findMany('api::notification.notification', {
        filters: { users_permissions_user: userId },
      });
      impact.notifications = userNotifications ? userNotifications.length : 0;

      return impact;

    } catch (error) {
      console.error('Error getting user deletion impact:', error);
      return null;
    }
  },
};
