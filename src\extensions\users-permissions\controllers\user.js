'use strict';

module.exports = {
  async updateMe(ctx) {
    const userId = ctx.state.user.id; // Authenticated user's ID
    const { firstName, lastName , username } = ctx.request.body;
    const updateData = { firstName, lastName , username };

    const updatedUser = await strapi
      .plugin('users-permissions')
      .service('user')
      .edit(userId, updateData);

    ctx.send({
      message: 'Profile updated successfully',
      user: updatedUser,
    });
  },

  async deleteMe(ctx) {
    try {
      const userId = ctx.state.user.id; // Authenticated user's ID
      const userEmail = ctx.state.user.email;

      console.log(`🗑️ User account deletion requested: ID ${userId}, Email: ${userEmail}`);

      // Optional: Require password confirmation for security
      const { password, confirmDeletion } = ctx.request.body;

      // Validate deletion confirmation
      if (!confirmDeletion || confirmDeletion !== 'DELETE_MY_ACCOUNT') {
        return ctx.badRequest({
          message: 'Account deletion must be confirmed. Please send confirmDeletion: "DELETE_MY_ACCOUNT"',
        });
      }

      // Optional: Verify password if provided
      if (password) {
        const user = await strapi.entityService.findOne('plugin::users-permissions.user', userId);
        const validPassword = await strapi.plugins['users-permissions'].services.user.validatePassword(password, user.password);

        if (!validPassword) {
          return ctx.badRequest({
            message: 'Invalid password provided',
          });
        }
      }

      // Clean up user-related data before deletion
      await strapi.service('api::user.user').cleanupUserData(userId);

      // Delete the user account
      await strapi.entityService.delete('plugin::users-permissions.user', userId);

      console.log(`✅ User account deleted successfully: ID ${userId}, Email: ${userEmail}`);

      // Send success response (user will be logged out)
      ctx.send({
        message: 'Your account has been permanently deleted',
        deleted: true,
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      console.error('❌ Error deleting user account:', error);

      // Don't expose internal errors to the user
      ctx.throw(500, 'An error occurred while deleting your account. Please try again or contact support.');
    }
  },
};
